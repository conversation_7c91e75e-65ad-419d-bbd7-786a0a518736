import { NextResponse } from 'next/server'
import prisma from '@/lib/database'

export async function GET() {
  try {
    const channels = await prisma.channel.findMany({
      include: {
        category: {
          select: {
            name: true
          }
        },
        country: {
          select: {
            name: true,
            flag: true
          }
        },
        language: {
          select: {
            name: true
          }
        },
        _count: {
          select: {
            channel_sources: true
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    })

    return NextResponse.json(channels)
  } catch (error) {
    console.error('Error fetching channels:', error)
    return NextResponse.json(
      { error: 'Failed to fetch channels' },
      { status: 500 }
    )
  }
}
