'use client'

import { useState } from 'react'
import { OverviewDashboard } from '@/components/overview-dashboard'
import { ChannelDetails } from '@/components/channel-details'
import { ChannelTable } from '@/components/channel-table'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { BarChart3, Tv, List, Plus } from 'lucide-react'

type ViewMode = 'overview' | 'channels' | 'channel-detail'

export default function DashboardPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('overview')
  const [selectedChannelId, setSelectedChannelId] = useState<string>('')

  const handleChannelClick = (channelId: string) => {
    if (channelId === 'all') {
      setViewMode('channels')
    } else {
      setSelectedChannelId(channelId)
      setViewMode('channel-detail')
    }
  }

  const handleBackToOverview = () => {
    setViewMode('overview')
    setSelectedChannelId('')
  }

  const handleBackToChannels = () => {
    setViewMode('channels')
    setSelectedChannelId('')
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold">Channel Manager</h1>
              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === 'overview' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={handleBackToOverview}
                >
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Overview
                </Button>
                <Button
                  variant={viewMode === 'channels' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('channels')}
                >
                  <List className="mr-2 h-4 w-4" />
                  Channels
                </Button>
              </div>
            </div>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Channel
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1">
        {viewMode === 'overview' && (
          <div className="p-6">
            <div className="mb-6">
              <h2 className="text-3xl font-bold">Dashboard Overview</h2>
              <p className="text-muted-foreground">
                Comprehensive view of your channel and source statistics
              </p>
            </div>
            <OverviewDashboard onChannelClick={handleChannelClick} />
          </div>
        )}

        {viewMode === 'channels' && (
          <div className="p-6">
            <div className="mb-6 flex justify-between items-center">
              <div>
                <h2 className="text-2xl font-semibold">Channel Management</h2>
                <p className="text-muted-foreground">
                  Manage all your channels and their sources
                </p>
              </div>
            </div>
            <ChannelTable />
          </div>
        )}

        {viewMode === 'channel-detail' && selectedChannelId && (
          <ChannelDetails
            channelId={selectedChannelId}
            onBack={handleBackToChannels}
          />
        )}
      </div>
    </div>
  )
}
