'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Globe, Languages, FolderOpen, Radio, ExternalLink, CheckCircle, XCircle } from 'lucide-react'

interface ChannelDetailsProps {
  channelId: string
  onBack: () => void
}

interface ChannelDetail {
  channel_id: string
  name: string
  alt_names?: string
  country_code?: string
  language_code?: string
  is_active: boolean
  created_at: string
  logo_url?: string
  category?: {
    name: string
  }
  country?: {
    name: string
    flag?: string
  }
  language?: {
    name: string
  }
  group?: {
    name: string
    is_featured: boolean
  }
  channel_sources: Array<{
    source_id: number
    source_url: string
    priority: number
    is_active: boolean
    last_checked?: string
    last_status?: boolean
    retry_count: number
    is_external: boolean
    source_provider?: {
      name: string
    }
  }>
}

export function ChannelDetails({ channelId, onBack }: ChannelDetailsProps) {
  const [channel, setChannel] = useState<ChannelDetail | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchChannel = async () => {
      try {
        const response = await fetch(`/api/channels/${channelId}`)
        if (response.ok) {
          const data = await response.json()
          setChannel(data)
        }
      } catch (error) {
        console.error('Error fetching channel details:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchChannel()
  }, [channelId])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!channel) {
    return (
      <div className="p-6">
        <Button onClick={onBack} variant="ghost" className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Overview
        </Button>
        <div>Channel not found</div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Button onClick={onBack} variant="ghost">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Overview
        </Button>
        <Badge variant={channel.is_active ? "default" : "secondary"}>
          {channel.is_active ? "Active" : "Inactive"}
        </Badge>
      </div>

      {/* Channel Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start space-x-4">
            {channel.logo_url && (
              <img 
                src={channel.logo_url} 
                alt={channel.name}
                className="w-16 h-16 rounded-lg object-cover"
              />
            )}
            <div className="flex-1">
              <CardTitle className="text-2xl">{channel.name}</CardTitle>
              {channel.alt_names && (
                <CardDescription className="mt-1">
                  Also known as: {channel.alt_names}
                </CardDescription>
              )}
              <div className="flex items-center space-x-4 mt-3">
                {channel.country && (
                  <div className="flex items-center space-x-1">
                    <Globe className="h-4 w-4" />
                    <span className="text-sm">
                      {channel.country.flag} {channel.country.name}
                    </span>
                  </div>
                )}
                {channel.language && (
                  <div className="flex items-center space-x-1">
                    <Languages className="h-4 w-4" />
                    <span className="text-sm">{channel.language.name}</span>
                  </div>
                )}
                {channel.category && (
                  <div className="flex items-center space-x-1">
                    <FolderOpen className="h-4 w-4" />
                    <span className="text-sm">{channel.category.name}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">Channel ID:</span>
              <p className="text-muted-foreground">{channel.channel_id}</p>
            </div>
            <div>
              <span className="font-medium">Created:</span>
              <p className="text-muted-foreground">
                {new Date(channel.created_at).toLocaleDateString()}
              </p>
            </div>
            <div>
              <span className="font-medium">Sources:</span>
              <p className="text-muted-foreground">{channel.channel_sources.length}</p>
            </div>
            <div>
              <span className="font-medium">Active Sources:</span>
              <p className="text-muted-foreground">
                {channel.channel_sources.filter(s => s.is_active).length}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Channel Sources */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Radio className="mr-2 h-5 w-5" />
            Channel Sources ({channel.channel_sources.length})
          </CardTitle>
          <CardDescription>
            All streaming sources for this channel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {channel.channel_sources.map((source) => (
              <div 
                key={source.source_id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <a 
                      href={source.source_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline flex items-center"
                    >
                      {source.source_url}
                      <ExternalLink className="ml-1 h-3 w-3" />
                    </a>
                    {source.is_external && (
                      <Badge variant="outline" className="text-xs">External</Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-xs text-muted-foreground">
                    {source.source_provider && (
                      <span>Provider: {source.source_provider.name}</span>
                    )}
                    <span>Priority: {source.priority}</span>
                    {source.last_checked && (
                      <span>
                        Last checked: {new Date(source.last_checked).toLocaleDateString()}
                      </span>
                    )}
                    {source.retry_count > 0 && (
                      <span>Retries: {source.retry_count}</span>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {source.last_status !== null && (
                    <div className="flex items-center">
                      {source.last_status ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                  )}
                  <Badge variant={source.is_active ? "default" : "secondary"}>
                    {source.is_active ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
            ))}
            {channel.channel_sources.length === 0 && (
              <p className="text-muted-foreground text-center py-4">
                No sources found for this channel
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
