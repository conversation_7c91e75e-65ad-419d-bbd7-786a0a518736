import { NextResponse } from 'next/server'
import { getChannelsByCountry } from '@/lib/database'

export async function GET() {
  try {
    const data = await getChannelsByCountry()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching channels by country:', error)
    return NextResponse.json(
      { error: 'Failed to fetch channels by country' },
      { status: 500 }
    )
  }
}
