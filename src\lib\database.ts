import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export interface DashboardStats {
  totalChannels: number
  activeChannels: number
  inactiveChannels: number
  totalSources: number
  activeSources: number
  inactiveSources: number
  totalCategories: number
  totalCountries: number
  totalLanguages: number
  totalProviders: number
}

export interface ChannelsByCategory {
  category: string
  count: number
  activeCount: number
  inactiveCount: number
}

export interface ChannelsByCountry {
  country: string
  countryCode: string
  count: number
  flag?: string
}

export interface ChannelsByLanguage {
  language: string
  languageCode: string
  count: number
}

export interface SourcesByProvider {
  provider: string
  count: number
  activeCount: number
  inactiveCount: number
}

export interface RecentChannels {
  channel_id: string
  name: string
  country_code?: string
  category?: string
  language_code?: string
  is_active: boolean
  created_at: Date
  logo_url?: string
}

export async function getDashboardStats(): Promise<DashboardStats> {
  const [
    totalChannels,
    activeChannels,
    totalSources,
    activeSources,
    totalCategories,
    totalCountries,
    totalLanguages,
    totalProviders
  ] = await Promise.all([
    prisma.channel.count(),
    prisma.channel.count({ where: { is_active: true } }),
    prisma.channelSource.count(),
    prisma.channelSource.count({ where: { is_active: true } }),
    prisma.category.count(),
    prisma.country.count(),
    prisma.language.count(),
    prisma.sourceProvider.count()
  ])

  return {
    totalChannels,
    activeChannels,
    inactiveChannels: totalChannels - activeChannels,
    totalSources,
    activeSources,
    inactiveSources: totalSources - activeSources,
    totalCategories,
    totalCountries,
    totalLanguages,
    totalProviders
  }
}

export async function getChannelsByCategory(): Promise<ChannelsByCategory[]> {
  const result = await prisma.category.findMany({
    select: {
      name: true,
      _count: {
        select: {
          channels: true
        }
      },
      channels: {
        select: {
          is_active: true
        }
      }
    },
    orderBy: {
      channels: {
        _count: 'desc'
      }
    }
  })

  return result.map(category => ({
    category: category.name,
    count: category._count.channels,
    activeCount: category.channels.filter(c => c.is_active).length,
    inactiveCount: category.channels.filter(c => !c.is_active).length
  }))
}

export async function getChannelsByCountry(): Promise<ChannelsByCountry[]> {
  const result = await prisma.country.findMany({
    select: {
      name: true,
      code: true,
      flag: true,
      _count: {
        select: {
          channels: true
        }
      }
    },
    where: {
      channels: {
        some: {}
      }
    },
    orderBy: {
      channels: {
        _count: 'desc'
      }
    },
    take: 20
  })

  return result.map(country => ({
    country: country.name,
    countryCode: country.code,
    count: country._count.channels,
    flag: country.flag || undefined
  }))
}

export async function getChannelsByLanguage(): Promise<ChannelsByLanguage[]> {
  const result = await prisma.language.findMany({
    select: {
      name: true,
      code: true,
      _count: {
        select: {
          channels: true
        }
      }
    },
    where: {
      channels: {
        some: {}
      }
    },
    orderBy: {
      channels: {
        _count: 'desc'
      }
    },
    take: 15
  })

  return result.map(language => ({
    language: language.name,
    languageCode: language.code,
    count: language._count.channels
  }))
}

export async function getSourcesByProvider(): Promise<SourcesByProvider[]> {
  const result = await prisma.sourceProvider.findMany({
    select: {
      name: true,
      _count: {
        select: {
          channel_sources: true
        }
      },
      channel_sources: {
        select: {
          is_active: true
        }
      }
    },
    orderBy: {
      channel_sources: {
        _count: 'desc'
      }
    }
  })

  return result.map(provider => ({
    provider: provider.name,
    count: provider._count.channel_sources,
    activeCount: provider.channel_sources.filter(s => s.is_active).length,
    inactiveCount: provider.channel_sources.filter(s => !s.is_active).length
  }))
}

export async function getRecentChannels(limit: number = 10): Promise<RecentChannels[]> {
  const result = await prisma.channel.findMany({
    select: {
      channel_id: true,
      name: true,
      country_code: true,
      language_code: true,
      is_active: true,
      created_at: true,
      logo_url: true,
      category: {
        select: {
          name: true
        }
      }
    },
    orderBy: {
      created_at: 'desc'
    },
    take: limit
  })

  return result.map(channel => ({
    channel_id: channel.channel_id,
    name: channel.name,
    country_code: channel.country_code || undefined,
    category: channel.category?.name,
    language_code: channel.language_code || undefined,
    is_active: channel.is_active,
    created_at: channel.created_at,
    logo_url: channel.logo_url || undefined
  }))
}

export async function getChannelDetails(channelId: string) {
  return await prisma.channel.findUnique({
    where: { channel_id: channelId },
    include: {
      category: true,
      country: true,
      language: true,
      group: true,
      channel_sources: {
        include: {
          source_provider: true
        }
      }
    }
  })
}

export default prisma
