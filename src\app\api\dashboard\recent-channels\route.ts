import { NextResponse } from 'next/server'
import { getRecentChannels } from '@/lib/database'

export async function GET() {
  try {
    const data = await getRecentChannels(10)
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching recent channels:', error)
    return NextResponse.json(
      { error: 'Failed to fetch recent channels' },
      { status: 500 }
    )
  }
}
