import { NextResponse } from 'next/server'
import { getChannelsByLanguage } from '@/lib/database'

export async function GET() {
  try {
    const data = await getChannelsByLanguage()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching channels by language:', error)
    return NextResponse.json(
      { error: 'Failed to fetch channels by language' },
      { status: 500 }
    )
  }
}
