import { NextResponse } from 'next/server'
import { getSourcesByProvider } from '@/lib/database'

export async function GET() {
  try {
    const data = await getSourcesByProvider()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching sources by provider:', error)
    return NextResponse.json(
      { error: 'Failed to fetch sources by provider' },
      { status: 500 }
    )
  }
}
