'use client'

import { useEffect, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { StatsCard } from '@/components/stats-card'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart,
} from 'recharts'
import {
  Tv,
  Radio,
  Globe,
  Languages,
  FolderOpen,
  Server,
  Activity,
  TrendingUp,
  Users,
  Eye,
} from 'lucide-react'
import {
  DashboardStats,
  ChannelsByCategory,
  ChannelsByCountry,
  ChannelsByLanguage,
  SourcesByProvider,
  RecentChannels,
} from '@/lib/database'

const COLORS = [
  '#0088FE',
  '#00C49F',
  '#FFBB28',
  '#FF8042',
  '#8884D8',
  '#82CA9D',
  '#FFC658',
  '#FF7C7C',
]

interface OverviewDashboardProps {
  onChannelClick?: (channelId: string) => void
}

export function OverviewDashboard({ onChannelClick }: OverviewDashboardProps) {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [channelsByCategory, setChannelsByCategory] = useState<
    ChannelsByCategory[]
  >([])
  const [channelsByCountry, setChannelsByCountry] = useState<
    ChannelsByCountry[]
  >([])
  const [channelsByLanguage, setChannelsByLanguage] = useState<
    ChannelsByLanguage[]
  >([])
  const [sourcesByProvider, setSourcesByProvider] = useState<
    SourcesByProvider[]
  >([])
  const [recentChannels, setRecentChannels] = useState<RecentChannels[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [
          statsData,
          categoriesData,
          countriesData,
          languagesData,
          providersData,
          recentData,
        ] = await Promise.all([
          fetch('/api/dashboard/stats').then(r => r.json()),
          fetch('/api/dashboard/channels-by-category').then(r => r.json()),
          fetch('/api/dashboard/channels-by-country').then(r => r.json()),
          fetch('/api/dashboard/channels-by-language').then(r => r.json()),
          fetch('/api/dashboard/sources-by-provider').then(r => r.json()),
          fetch('/api/dashboard/recent-channels').then(r => r.json()),
        ])

        setStats(statsData)
        setChannelsByCategory(categoriesData)
        setChannelsByCountry(countriesData)
        setChannelsByLanguage(languagesData)
        setSourcesByProvider(providersData)
        setRecentChannels(recentData)
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!stats) {
    return <div>Error loading dashboard data</div>
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
        <StatsCard
          title="Total Channels"
          value={stats.totalChannels}
          subtitle={`${stats.activeChannels} active, ${stats.inactiveChannels} inactive`}
          icon={Tv}
          onClick={() => onChannelClick?.('all')}
        />
        <StatsCard
          title="Total Sources"
          value={stats.totalSources}
          subtitle={`${stats.activeSources} active, ${stats.inactiveSources} inactive`}
          icon={Radio}
        />
        <StatsCard
          title="Categories"
          value={stats.totalCategories}
          icon={FolderOpen}
        />
        <StatsCard
          title="Countries"
          value={stats.totalCountries}
          icon={Globe}
        />
        <StatsCard
          title="Languages"
          value={stats.totalLanguages}
          icon={Languages}
        />
        <StatsCard
          title="Providers"
          value={stats.totalProviders}
          icon={Server}
        />
        <StatsCard
          title="Active Rate"
          value={Math.round((stats.activeChannels / stats.totalChannels) * 100)}
          subtitle="% of channels active"
          icon={Activity}
        />
        <StatsCard
          title="Source Rate"
          value={
            Math.round((stats.totalSources / stats.totalChannels) * 100) / 100
          }
          subtitle="sources per channel"
          icon={TrendingUp}
        />
      </div>

      {/* Charts Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Channels by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Channels by Category</CardTitle>
            <CardDescription>
              Distribution of channels across categories
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={channelsByCategory}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="category"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Active vs Inactive Channels */}
        <Card>
          <CardHeader>
            <CardTitle>Channel Status</CardTitle>
            <CardDescription>Active vs inactive channels</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={[
                    { name: 'Active', value: stats.activeChannels },
                    { name: 'Inactive', value: stats.inactiveChannels },
                  ]}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) =>
                    name && typeof percent === 'number'
                      ? `${name} ${(percent * 100).toFixed(0)}%`
                      : ''
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  <Cell fill="#00C49F" />
                  <Cell fill="#FF8042" />
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* More Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Top Countries */}
        <Card>
          <CardHeader>
            <CardTitle>Top Countries</CardTitle>
            <CardDescription>Countries with most channels</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={channelsByCountry.slice(0, 10)}
                layout="horizontal"
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="country" type="category" width={80} />
                <Tooltip />
                <Bar dataKey="count" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Top Languages */}
        <Card>
          <CardHeader>
            <CardTitle>Top Languages</CardTitle>
            <CardDescription>Most popular languages</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={channelsByLanguage.slice(0, 8)}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                  label={({ language, percent }) =>
                    language && typeof percent === 'number' && percent > 0.05
                      ? `${language} ${(percent * 100).toFixed(0)}%`
                      : ''
                  }
                >
                  {channelsByLanguage.slice(0, 8).map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Channels and Sources by Provider */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Channels */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Channels</CardTitle>
            <CardDescription>
              Latest channels added to the system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentChannels.map(channel => (
                <div
                  key={channel.channel_id}
                  className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 cursor-pointer"
                  onClick={() => onChannelClick?.(channel.channel_id)}
                >
                  {channel.logo_url && (
                    <img
                      src={channel.logo_url}
                      alt={channel.name}
                      className="w-8 h-8 rounded object-cover"
                    />
                  )}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {channel.name}
                    </p>
                    <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                      {channel.category && <span>{channel.category}</span>}
                      {channel.country_code && (
                        <span>{channel.country_code}</span>
                      )}
                      <span>
                        {new Date(channel.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <div
                    className={`w-2 h-2 rounded-full ${
                      channel.is_active ? 'bg-green-500' : 'bg-gray-400'
                    }`}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Sources by Provider */}
        <Card>
          <CardHeader>
            <CardTitle>Sources by Provider</CardTitle>
            <CardDescription>
              Distribution of sources across providers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={sourcesByProvider}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="provider"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis />
                <Tooltip />
                <Bar
                  dataKey="activeCount"
                  stackId="a"
                  fill="#00C49F"
                  name="Active"
                />
                <Bar
                  dataKey="inactiveCount"
                  stackId="a"
                  fill="#FF8042"
                  name="Inactive"
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
