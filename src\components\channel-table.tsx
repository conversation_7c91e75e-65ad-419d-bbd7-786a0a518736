'use client'

import { useEffect, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Search, Filter, Eye, Edit, Trash2 } from 'lucide-react'

interface Channel {
  channel_id: string
  name: string
  alt_names?: string
  country_code?: string
  language_code?: string
  is_active: boolean
  created_at: string
  logo_url?: string
  category?: {
    name: string
  }
  country?: {
    name: string
    flag?: string
  }
  language?: {
    name: string
  }
  _count?: {
    channel_sources: number
  }
}

export function ChannelTable() {
  const [channels, setChannels] = useState<Channel[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterActive, setFilterActive] = useState<
    'all' | 'active' | 'inactive'
  >('all')

  useEffect(() => {
    const fetchChannels = async () => {
      try {
        const response = await fetch('/api/channels')
        if (response.ok) {
          const data = await response.json()
          setChannels(data)
        }
      } catch (error) {
        console.error('Error fetching channels:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchChannels()
  }, [])

  const filteredChannels = channels.filter(channel => {
    const matchesSearch =
      channel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      channel.channel_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (channel.alt_names &&
        channel.alt_names.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesFilter =
      filterActive === 'all' ||
      (filterActive === 'active' && channel.is_active) ||
      (filterActive === 'inactive' && !channel.is_active)

    return matchesSearch && matchesFilter
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Channels ({filteredChannels.length})</CardTitle>
        <CardDescription>
          Manage your channels and their streaming sources
        </CardDescription>

        {/* Search and Filter */}
        <div className="flex items-center space-x-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <input
              placeholder="Search channels..."
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={filterActive === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilterActive('all')}
            >
              All
            </Button>
            <Button
              variant={filterActive === 'active' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilterActive('active')}
            >
              Active
            </Button>
            <Button
              variant={filterActive === 'inactive' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilterActive('inactive')}
            >
              Inactive
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {filteredChannels.map(channel => (
            <div
              key={channel.channel_id}
              className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
            >
              <div className="flex items-center space-x-4">
                {channel.logo_url && (
                  <img
                    src={channel.logo_url}
                    alt={channel.name}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                )}
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold">{channel.name}</h3>
                    <Badge
                      variant={channel.is_active ? 'default' : 'secondary'}
                    >
                      {channel.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-4 mt-1 text-sm text-muted-foreground">
                    <span>ID: {channel.channel_id}</span>
                    {channel.category && (
                      <span>Category: {channel.category.name}</span>
                    )}
                    {channel.country && (
                      <span>
                        {channel.country.flag} {channel.country.name}
                      </span>
                    )}
                    {channel.language && (
                      <span>Lang: {channel.language.name}</span>
                    )}
                    {channel._count && (
                      <span>Sources: {channel._count.channel_sources}</span>
                    )}
                  </div>
                  {channel.alt_names && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Also: {channel.alt_names}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button size="sm" variant="outline">
                  <Eye className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="outline">
                  <Edit className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="outline">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
          {filteredChannels.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              {searchTerm || filterActive !== 'all'
                ? 'No channels match your search criteria'
                : 'No channels found'}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
