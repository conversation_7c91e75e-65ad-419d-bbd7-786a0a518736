import { NextResponse } from 'next/server'
import { getChannelsByCategory } from '@/lib/database'

export async function GET() {
  try {
    const data = await getChannelsByCategory()
    return NextResponse.json(data)
  } catch (error) {
    console.error('Error fetching channels by category:', error)
    return NextResponse.json(
      { error: 'Failed to fetch channels by category' },
      { status: 500 }
    )
  }
}
