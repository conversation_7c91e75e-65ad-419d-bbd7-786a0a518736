import { NextResponse } from 'next/server'
import { getChannelDetails } from '@/lib/database'

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const channel = await getChannelDetails(params.id)
    
    if (!channel) {
      return NextResponse.json(
        { error: 'Channel not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(channel)
  } catch (error) {
    console.error('Error fetching channel details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch channel details' },
      { status: 500 }
    )
  }
}
